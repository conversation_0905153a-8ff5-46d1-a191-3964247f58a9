---
description: 
globs: 
alwaysApply: true
---
# 配置管理指南

## 配置概览

文件传输服务支持灵活的多用户配置，每个用户可以有独立的存储路径、传输限速和文件大小限制。

## 配置文件

### 主配置文件

服务端配置通过Spring Boot的application.yml进行管理，配置示例在 [example-auth-config.yml](mdc:example-auth-config.yml) 中。

### 配置结构

```yaml
file:
  transfer:
    server:
      enabled: true
      database-path: ./data/file-transfer/database.db
      
      # 清理配置
      cleanup-interval: 3600000        # 1小时（毫秒）
      record-expire-time: 86400000     # 24小时（毫秒）
      
      # 默认配置（兜底配置）
      default:
        storage-path: ./data/file-transfer/files
        upload-rate-limit: 10485760    # 10MB/s
        download-rate-limit: 10485760  # 10MB/s
        default-chunk-size: 2097152    # 2MB
        max-file-size: 104857600       # 100MB
        max-in-memory-size: 10485760   # 10MB
        fast-upload-enabled: true
        rate-limit-enabled: true
      
      # 用户配置
      users:
        user1:
          secret-key: "user1-secret-2024"
          storage-path: ./data/users/user1
          upload-rate-limit: 2097152   # 2MB/s
          download-rate-limit: 2097152
          max-file-size: 52428800      # 50MB
```

## 配置层次结构

1. **全局默认配置**: `file.transfer.server.default.*`
2. **用户特定配置**: `file.transfer.server.users.{username}.*`
3. **运行时配置**: 通过API动态调整

## 配置管理组件

### 配置类

配置管理在 [config/](mdc:file-transfer-server-sdk/src/main/java/com/sdesrd/filetransfer/server/config) 包中：

- **FileTransferServerConfig**: 主配置类，映射application.yml配置
- **UserConfig**: 用户级别配置
- **DefaultConfig**: 默认配置兜底

### 配置服务

- **[UserConfigService](mdc:file-transfer-server-sdk/src/main/java/com/sdesrd/filetransfer/server/service)**: 用户配置管理服务
- 提供配置读取、验证、合并等功能
- 支持配置热更新（重启生效）

## 用户认证配置

### 密钥管理

每个用户必须配置独立的secretKey：

```yaml
users:
  user1:
    secret-key: "user1-secret-2024"  # 用于HMAC-SHA256签名
  vip:
    secret-key: "vip-secret-2024"
```

### 认证流程

1. 客户端使用用户名和密钥生成请求签名
2. 服务端根据用户名查找对应的secretKey
3. 服务端验证请求签名的有效性
4. 应用该用户的个性化配置

## 差异化配置

### 用户类型示例

```yaml
users:
  # 基础用户
  basic_user:
    secret-key: "basic-secret"
    upload-rate-limit: 1048576      # 1MB/s
    max-file-size: 10485760         # 10MB
    
  # VIP用户  
  vip_user:
    secret-key: "vip-secret"
    upload-rate-limit: 10485760     # 10MB/s
    max-file-size: 104857600        # 100MB
    
  # 管理员
  admin:
    secret-key: "admin-secret"
    upload-rate-limit: 52428800     # 50MB/s
    max-file-size: 1073741824       # 1GB
    rate-limit-enabled: false       # 不限速
```

## 配置验证

### 配置检查

服务启动时会自动验证配置：

1. 必需字段检查（用户名、密钥）
2. 数值范围验证（速率限制、文件大小）
3. 路径有效性检查
4. 配置冲突检测

### 配置合并策略

用户配置继承默认配置，用户特定值覆盖默认值：

```
最终配置 = 默认配置 + 用户配置覆盖
```

## 配置最佳实践

### 安全性

1. **密钥管理**: 使用强随机密钥，定期轮换
2. **路径隔离**: 不同用户使用独立存储路径
3. **权限控制**: 文件系统权限与配置对齐

### 性能优化

1. **分块大小**: 根据网络情况调整chunk-size
2. **内存使用**: 合理设置max-in-memory-size
3. **并发控制**: 根据服务器性能调整并发数

### 监控配置

1. **清理策略**: 定期清理过期记录和临时文件
2. **存储监控**: 监控各用户存储空间使用情况
3. **性能监控**: 追踪传输速度和成功率

## 动态配置

### 配置更新

目前配置更新需要重启服务，未来版本将支持：

1. 热更新部分配置项
2. 通过管理API动态调整
3. 配置变更通知机制

## 客户端配置

客户端配置示例请参考 [client-sdk-guide.mdc](mdc:.cursor/rules/client-sdk-guide.mdc)。

## 集成指南

详细的集成配置步骤请参考 [integration-guide.md](mdc:integration-guide.md)。

# 配置指南

## 服务端配置

### 基础配置
服务端配置通过 `application.yml` 文件进行管理，主要配置项如下：

```yaml
file:
  transfer:
    server:
      enabled: true
      database-path: ./data/file-transfer/database.db
      
      # 清理配置
      cleanup-interval: 3600000        # 1小时（毫秒）
      record-expire-time: 86400000     # 24小时（毫秒）
```

### 用户配置
每个用户可以配置独立的存储路径和限速策略：

```yaml
file:
  transfer:
    server:
      users:
        user1:
          secret-key: "user1-secret-2024"
          storage-path: ./data/users/user1
          upload-rate-limit: 2097152    # 2MB/s
          download-rate-limit: 2097152
          max-file-size: 52428800       # 50MB
```

### 默认配置
当用户未指定特定配置时，将使用默认配置：

```yaml
file:
  transfer:
    server:
      default:
        storage-path: ./data/file-transfer/files
        upload-rate-limit: 10485760     # 10MB/s
        download-rate-limit: 10485760
        default-chunk-size: 2097152     # 2MB
        max-file-size: 104857600        # 100MB
        max-in-memory-size: 10485760    # 10MB
        fast-upload-enabled: true
        rate-limit-enabled: true
```

## 客户端配置

### 基础配置
客户端通过 `ClientConfig` 类进行配置：

```java
ClientConfig config = new ClientConfig();
config.getAuth().setServerAddr("your-domain.com");
config.getAuth().setServerPort(49011);
config.getAuth().setUser("user1");
config.getAuth().setSecretKey("user1-secret-2024");
```

### 传输配置
可以配置分块大小和并发传输数：

```java
config.setChunkSize(2 * 1024 * 1024);  // 2MB
config.setMaxConcurrentTransfers(3);
```

## 配置项说明

### 服务端配置项
- `enabled`: 是否启用文件传输服务
- `database-path`: SQLite数据库文件路径
- `cleanup-interval`: 清理过期记录的间隔时间
- `record-expire-time`: 记录过期时间
- `storage-path`: 文件存储路径
- `upload-rate-limit`: 上传速度限制（字节/秒）
- `download-rate-limit`: 下载速度限制（字节/秒）
- `max-file-size`: 最大文件大小限制
- `fast-upload-enabled`: 是否启用秒传功能
- `rate-limit-enabled`: 是否启用限速功能

### 客户端配置项
- `serverAddr`: 服务器地址
- `serverPort`: 服务器端口
- `user`: 用户名
- `secretKey`: 密钥
- `chunkSize`: 分块大小
- `maxConcurrentTransfers`: 最大并发传输数

## 配置最佳实践
1. 根据实际需求设置合理的限速值
2. 为不同用户配置独立的存储路径
3. 定期清理过期记录
4. 根据服务器性能调整并发传输数
5. 合理设置分块大小，建议2MB-5MB之间
